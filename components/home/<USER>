import React from "react";
import Image from "next/image";
import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import featureCardsData from "@/data/featureCards.json";

// Custom SVG Icon component to handle coloring
const CustomIcon = ({ src, alt, color }) => {
  return (
    <div className="w-12 h-12 flex items-center justify-center">
      <Image
        src={src}
        alt={alt}
        width={48}
        height={48}
        className="w-12 h-12"
        style={{
          filter: color === "#A259FF"
            ? "brightness(0) saturate(100%) invert(47%) sepia(99%) saturate(7471%) hue-rotate(267deg) brightness(101%) contrast(101%)"
            : color === "#04CE78"
            ? "brightness(0) saturate(100%) invert(69%) sepia(99%) saturate(1837%) hue-rotate(120deg) brightness(96%) contrast(101%)"
            : color === "#FFB86B"
            ? "brightness(0) saturate(100%) invert(78%) sepia(99%) saturate(1837%) hue-rotate(15deg) brightness(101%) contrast(101%)"
            : "none"
        }}
      />
    </div>
  );
};

const FeatureCards = () => {
  const { cards } = featureCardsData;

  return (
    <div className="w-full flex flex-col lg:flex-row gap-4 lg:gap-6 mt-8">
      {cards.map((card) => (
        <Card
          key={card.id}
          className={`flex-1 border-0 shadow-sm hover:shadow-md transition-all duration-300 ${card.backgroundColor}`}
        >
          <CardContent className="p-6 lg:p-8 flex flex-col items-start h-full">
            {/* Icon Container */}
            <div className="mb-6 p-3 bg-white rounded-2xl shadow-sm">
              <CustomIcon
                src={card.icon}
                alt={`${card.title} icon`}
                color={card.iconColor}
              />
            </div>

            {/* Content */}
            <h3 className="font-bold text-lg lg:text-xl mb-3 text-gray-900 leading-tight">
              {card.title}
            </h3>

            <p className="text-sm lg:text-base text-gray-700 mb-6 leading-relaxed flex-grow">
              {card.description}
            </p>

            {/* CTA Link */}
            <Link
              href={card.cta.href}
              className="mt-auto font-semibold text-gray-900 hover:text-gray-700 flex items-center gap-2 group transition-colors duration-200"
            >
              {card.cta.label}
              <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
            </Link>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default FeatureCards;